"use client"

import type React from "react"
import { Inter } from "next/font/google"
import "./globals.css"
import { SidebarProvider } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

const inter = Inter({ subsets: ["latin"] })

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [sidebarOpen, setSidebarOpen] = useState(true)

  useEffect(() => {
    // Check if user is authenticated
    const user = localStorage.getItem("user")
    const wasAuthenticated = isAuthenticated
    const nowAuthenticated = !!user

    setIsAuthenticated(nowAuthenticated)
    setIsLoading(false)

    // If user just signed in (transition from unauthenticated to authenticated),
    // force sidebar to be open and clear any previous sidebar state cookie
    if (!wasAuthenticated && nowAuthenticated) {
      setSidebarOpen(true)
      // Set the sidebar state cookie to ensure it opens
      document.cookie = "sidebar_state=true; path=/; max-age=" + (60 * 60 * 24 * 7)
    }
  }, [isAuthenticated])

  // Additional effect to handle initial page load for already authenticated users
  useEffect(() => {
    const user = localStorage.getItem("user")
    if (user && !isLoading) {
      // For authenticated users, always start with sidebar open
      setSidebarOpen(true)
      // Set the sidebar state cookie to ensure it opens
      document.cookie = "sidebar_state=true; path=/; max-age=" + (60 * 60 * 24 * 7)
    }
  }, [isLoading])

  // Show landing page for unauthenticated users or on landing route
  if (pathname === "/landing" || (!isAuthenticated && !isLoading)) {
    return (
      <html lang="en">
        <body className={inter.className}>{children}</body>
      </html>
    )
  }

  // Show loading state
  if (isLoading) {
    return (
      <html lang="en">
        <body className={inter.className}>
          <div className="min-h-screen flex items-center justify-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          </div>
        </body>
      </html>
    )
  }

  // Show authenticated app
  return (
    <html lang="en">
      <body className={inter.className}>
        <SidebarProvider open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <AppSidebar />
          <main className="flex-1">{children}</main>
        </SidebarProvider>
      </body>
    </html>
  )
}
